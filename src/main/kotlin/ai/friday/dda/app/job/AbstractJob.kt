package ai.friday.dda.app.job

import ai.friday.dda.andAppend
import ai.friday.dda.app.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.dda.app.interfaces.FeatureConfiguration
import ai.friday.dda.log
import io.micronaut.core.convert.ConversionService
import io.micronaut.http.annotation.Trace
import io.micronaut.scheduling.TaskExecutors
import io.micronaut.scheduling.TaskScheduler
import io.micronaut.tracing.annotation.NewSpan
import jakarta.inject.Inject
import jakarta.inject.Named
import java.time.Duration
import java.time.Instant
import java.time.ZonedDateTime
import java.util.concurrent.ScheduledFuture
import net.javacrumbs.shedlock.core.LockConfiguration
import net.javacrumbs.shedlock.core.LockProvider
import net.javacrumbs.shedlock.core.SimpleLock
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

abstract class AbstractJob(
    private val crons: List<String>,
    private val fixedDelay: Duration? = null,
    private val lockAtLeastFor: String? = null,
    private val lockAtMostFor: String? = null,
    private val shouldLock: Boolean = true,
    internal val shutdownGracefully: Boolean = true,
    internal val shutdownGracefullyMaxWaitTime: Int = 2,
) : Runnable {
    constructor(
        cron: String,
        lockAtLeastFor: String? = null,
        lockAtMostFor: String? = null,
        shutdownGracefully: Boolean = true,
        shutdownGracefullyMaxWaitTime: Int = 2,
        shouldLock: Boolean = true,
    ) : this(
        crons = listOf(cron),
        fixedDelay = null,
        lockAtLeastFor = lockAtLeastFor,
        lockAtMostFor = lockAtMostFor,
        shutdownGracefully = shutdownGracefully,
        shutdownGracefullyMaxWaitTime = shutdownGracefullyMaxWaitTime,
        shouldLock = shouldLock,
    )

    constructor(
        fixedDelay: Duration,
        lockAtLeastFor: String? = null,
        lockAtMostFor: String? = null,
        shutdownGracefully: Boolean = true,
        shutdownGracefullyMaxWaitTime: Int = 2,
        shouldLock: Boolean = true,
    ) : this(
        crons = listOf(),
        fixedDelay = fixedDelay,
        lockAtLeastFor = lockAtLeastFor,
        lockAtMostFor = lockAtMostFor,
        shutdownGracefully = shutdownGracefully,
        shutdownGracefullyMaxWaitTime = shutdownGracefullyMaxWaitTime,
        shouldLock = shouldLock,
    )

    internal val jobName: String = this.javaClass.originalSimpleName()

    private lateinit var lockAtLeastForDuration: Duration
    private lateinit var lockAtMostForDuration: Duration

    internal var running: Boolean = false
    private var lastStartTime: ZonedDateTime? = null
    private var lastElapsedMinutes: Long? = null

    private val schedules = mutableListOf<ScheduledFuture<*>>()
    private var lock: SimpleLock? = null

    @Inject
    @Named(TaskExecutors.SCHEDULED)
    private lateinit var taskScheduler: TaskScheduler

    @Inject
    private lateinit var lockProvider: LockProvider

    @Inject
    private lateinit var conversionService: ConversionService

    @Inject
    private lateinit var featureConfiguration: FeatureConfiguration

    internal abstract fun execute()

    @Trace
    @NewSpan
    override fun run() {
        if (featureConfiguration.maintenanceMode) {
            LOG.info(log("jobExcecutionSkipped" to "maintenanceMode"), "$jobName#run")
            return
        }

        if (shouldLock) {
            lock = acquireLock()
            if (lock == null) {
                LOG.info(log("jobExcecutionSkipped" to "could not acquire lock"), "$jobName#run")
                return
            }
        }

        lastStartTime = getZonedDateTime()

        LOG.info(log("step" to "begin"), "$jobName#run")
        try {
            running = true

            execute()
        } catch (t: Throwable) {
            LOG.error("$jobName#run", t)
        } finally {
            running = false
            unlock()

            val end = getZonedDateTime().toInstant()
            lastElapsedMinutes = Duration.between(lastStartTime!!.toInstant(), end).toMinutes()

            LOG.info(Markers.append("step", "end").andAppend("elapsedMinutes", lastElapsedMinutes), "$jobName#run")
        }
    }

    fun initialize(defaultLockAtLeastFor: String, defaultLockAtMostFor: String) {
        val markers = Markers.append("crons", crons)
            .andAppend("lockAtLeastFor", lockAtLeastFor)
            .andAppend("lockAtMostFor", lockAtMostFor)
            .andAppend("defaultLockAtLeastFor", defaultLockAtLeastFor)
            .andAppend("defaultLockAtMostFor", defaultLockAtMostFor)

        try {
            lockAtLeastForDuration = parseDuration(lockAtLeastFor ?: defaultLockAtLeastFor)
            lockAtMostForDuration = parseDuration(lockAtMostFor ?: defaultLockAtMostFor)

            crons.forEach { schedules.add(taskScheduler.schedule(it, this)) }
            fixedDelay?.let { schedules.add(taskScheduler.scheduleWithFixedDelay(null, it, this)) }

            LOG.info(markers, "$jobName#initialize")
        } catch (e: Exception) {
            LOG.error(markers, "$jobName#initialize", e)
        }
    }

    fun beginShutdown() {
        LOG.info("$jobName#beginShutdown")
        schedules.removeAll {
            it.cancel(false)
            true
        }
    }

    private fun acquireLock(): SimpleLock? {
        return try {
            lockProvider.lock(
                LockConfiguration(
                    Instant.now(),
                    jobName,
                    lockAtMostForDuration,
                    lockAtLeastForDuration,
                ),
            ).orElse(null)
        } catch (e: Exception) {
            LOG.error("$jobName#acquireLock", e)
            null
        }
    }

    internal fun unlock() {
        LOG.info("$jobName#unlock")
        lock?.unlock()
        lock = null
    }

    private fun parseDuration(value: String): Duration {
        return conversionService.convert(value, Duration::class.java).get()
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(AbstractJob::class.java)
    }
}

fun Class<*>.originalSimpleName(): String = simpleName.split("\$").first { it.isNotBlank() }