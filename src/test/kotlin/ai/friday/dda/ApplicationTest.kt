package ai.friday.dda

import io.kotest.core.spec.style.FunSpec
import io.micronaut.runtime.EmbeddedApplication
import io.micronaut.test.extensions.kotest5.annotation.MicronautTest
import utils.MicronautPropertiesTest

@MicronautTest
@MicronautPropertiesTest
internal open class ApplicationTest(
    private val application: EmbeddedApplication<*>
) : FunSpec({

    test("test the server is running") {
        assert(application.isRunning)
    }
})