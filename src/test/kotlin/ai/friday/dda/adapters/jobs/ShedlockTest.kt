package ai.friday.dda.adapters.jobs

import ai.friday.dda.DynamoDBUtils
import ai.friday.dda.adapters.dynamodb.SHEDLOCK_TABLE_NAME
import ai.friday.dda.app.job.AbstractJob
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import io.kotest.core.spec.style.AnnotationSpec
import io.kotest.matchers.shouldBe
import io.micronaut.test.extensions.kotest5.annotation.MicronautTest
import jakarta.inject.Singleton
import org.slf4j.LoggerFactory
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable
import software.amazon.awssdk.enhanced.dynamodb.TableSchema
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import utils.MicronautPropertiesTest

@Singleton
open class ShedlockJob : AbstractJob(cron = "59 23 * * *", lockAtLeastFor = "10m", lockAtMostFor = "20m") {
    private val logger = LoggerFactory.getLogger(ShedlockJob::class.java)

    override fun execute() {
        logger.info("ShedlockJob#execute")
    }
}

@DynamoDbBean
open class ShedlockEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = "_id")
    lateinit var primaryKey: String

    @get:DynamoDbAttribute(value = "lockedAt")
    lateinit var lockedAt: String

    @get:DynamoDbAttribute(value = "lockedBy")
    lateinit var lockedBy: String

    @get:DynamoDbAttribute(value = "lockUntil")
    lateinit var lockUntil: String
}

@MicronautTest
@MicronautPropertiesTest
class ShedlockTest(private val job: ShedlockJob) : AnnotationSpec() {
    private lateinit var table: DynamoDbTable<*>

    @BeforeEach
    fun setUp() {
        val clis = DynamoDBUtils.setup()

        table = clis.enhancedClient.table(SHEDLOCK_TABLE_NAME, TableSchema.fromBean(ShedlockEntity::class.java))
    }

    @Test
    fun `should lock`() {
        job.run()

        Thread.sleep(2_000)

        table.scan().items().toList().map(jacksonObjectMapper()::writeValueAsString).map { ObjectMapper().readValue(it, ShedlockEntity::class.java) }.filter { it.primaryKey.contains("Shedlock") }.size shouldBe 1
    }
}