package ai.friday.dda.adapters

import ai.friday.dda.PIC_PAY_ENV
import ai.friday.dda.adapters.arbi.DDABillsBatchProcessor
import ai.friday.dda.adapters.arbi.SendMessageProcessor
import io.kotest.core.spec.style.AnnotationSpec
import io.kotest.matchers.booleans.shouldBeFalse
import io.kotest.matchers.booleans.shouldBeTrue
import io.micronaut.http.annotation.Trace
import io.micronaut.test.extensions.kotest5.annotation.MicronautTest
import io.micronaut.tracing.annotation.NewSpan
import io.mockk.mockk
import jakarta.inject.Singleton
import java.io.ByteArrayOutputStream
import java.io.PrintStream
import org.slf4j.LoggerFactory

@Singleton
open class LogbackTest {
    private val logger = LoggerFactory.getLogger(LogbackTest::class.java)

    @Trace
    @NewSpan
    open fun foo() {
        logger.info("LogbackTest#foo")
    }
}

@MicronautTest(environments = [PIC_PAY_ENV, "stgpicpay"])
class PicPayLogbackIntegrationTest(private val test: LogbackTest) : AnnotationSpec() {
    @Test
    @Ignore
    fun test() {
        val outputStream = ByteArrayOutputStream()
        val originalOut = System.out // Keep a reference to the original stdout

        System.setOut(PrintStream(outputStream))

        test.foo()

        System.setOut(originalOut)
        val capturedOutput = outputStream.toString().trim { it <= ' ' }

        capturedOutput.contains("trace_id").shouldBeTrue()
        capturedOutput.contains("span_id").shouldBeTrue()
        capturedOutput.contains("trace_flags").shouldBeTrue()
    }
}

@MicronautTest(environments = ["staging"])
class FridayLogbackIntegrationTest(private val test: LogbackTest) : AnnotationSpec() {
    @Test
    @Ignore
    fun test() {
        val outputStream = ByteArrayOutputStream()
        val originalOut = System.out // Keep a reference to the original stdout

        System.setOut(PrintStream(outputStream))

        test.foo()

        System.setOut(originalOut)
        val capturedOutput = outputStream.toString().trim { it <= ' ' }

        capturedOutput.contains("trace_id").shouldBeFalse()
        capturedOutput.contains("span_id").shouldBeFalse()
        capturedOutput.contains("trace_flags").shouldBeFalse()
    }
}