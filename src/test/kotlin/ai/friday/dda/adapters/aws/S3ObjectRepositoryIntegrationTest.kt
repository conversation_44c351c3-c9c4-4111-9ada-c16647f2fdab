package ai.friday.dda.adapters.aws

import io.kotest.core.annotation.Ignored
import io.kotest.core.spec.style.FunSpec
import io.kotest.matchers.shouldNotBe
import io.micronaut.test.extensions.kotest5.annotation.MicronautTest

@MicronautTest
@Ignored
internal class S3ObjectRepositoryIntegrationTest(private val s3ObjectRepository: S3ObjectRepository) : FunSpec({
    val bucketName = "fake-bucket"

    test("deve listar os arquivos existentes") {
        val keys = s3ObjectRepository.listObjectKeys(
            bucketName,
            "user_documents/ACCOUNT-********-18ba-4513-9c89-2544cc1f6967/"
        )
        keys.size shouldNotBe 0
    }

    test("deve retornar o inputstream de um arquivo existente no diretorio") {
        val keys = s3ObjectRepository.listObjectKeys(
            bucketName,
            "user_documents/ACCOUNT-********-18ba-4513-9c89-2544cc1f6967/"
        )
        val inputStream = s3ObjectRepository.loadObject(bucketName, keys.first())
        inputStream.use {
            println(String(it.readAllBytes()))
        }
    }

    test("deve mover o arquivo para outra pasta") {
        s3ObjectRepository.moveObject(
            bucketName,
            "testefrom/123.txt",
            "testeto/123.txt"
        )
    }
})