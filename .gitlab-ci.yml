image: ${CI_DEPENDENCY_PROXY_GROUP_IMAGE_PREFIX}/eclipse-temurin:17-jdk

include:
  - template: Code-Quality.gitlab-ci.yml
  - template: Workflows/MergeRequest-Pipelines.gitlab-ci.yml
  - template: Security/SAST.gitlab-ci.yml
  - project: "via1/gitlab-ci-environment"
    file: "/envs/all-dda-service.yml"
    ref: "main"

code_quality:
  rules:
    - if: '$CODE_QUALITY_DISABLED'
      when: never
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"' # Run code quality job in merge request pipelines
    - if: '$CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH'      # Run code quality job in pipelines on the master branch (but not in other branch pipelines)
    - if: '$CI_COMMIT_TAG'
      when: never

stages:
  - build
  - test
  - release
  - release_environments
  - deploy

sast:
  stage: test
  artifacts:
    paths: [ gl-sast-report.json, gl-code-quality-report.json ]
  when: manual
  allow_failure: true

variables:
  CONTAINER_RELEASE_IMAGE: $CI_REGISTRY_IMAGE:$CI_COMMIT_SHORT_SHA
  MAVEN_OPTS: "-Dhttps.protocols=TLSv1.2 -Dmaven.repo.local=$CI_PROJECT_DIR/.m2/repository -Dorg.slf4j.simpleLogger.log.org.apache.maven.cli.transfer.Slf4jMavenTransferListener=WARN -Dorg.slf4j.simpleLogger.showDateTime=true -Djava.awt.headless=true"
  MAVEN_CLI_OPTS: "--batch-mode --errors --fail-at-end --show-version -DinstallAtEnd=true -DdeployAtEnd=true"
  TASK_DEFINITION_NAME: "dda-bills-task"
  CLUSTER_NAME: "bill-payment-cluster"
  SERVICE_NAME: "dda-bills-service"
  RELEASE_TRIGGER_COMMIT_MESSAGE: "/#deploy(stg|modattastg|mepoupestg|motorola(stg)?)/"
  VERSION: "$CI_COMMIT_REF_SLUG-$CI_COMMIT_SHORT_SHA"

before_script:
  - chmod +x mvnw

build:
  stage: build
  image:
    name: ${CI_DEPENDENCY_PROXY_GROUP_IMAGE_PREFIX}/eclipse-temurin:17-jdk
    pull_policy: if-not-present
  cache:
    paths:
      - .m2/repository
  script:
    - LOGGER_LEVELS_ROOT=OFF ./mvnw $MAVEN_CLI_OPTS jacoco:prepare-agent test jacoco:report verify -s ci/settings.xml
    - echo -n "Code coverage is "; grep -m1 -Po '(?<=<td class="ctr2">).*?(?=</td>)' target/site/jacoco/index.html | head -n1
  artifacts:
    paths:
      - target/site/jacoco
      - target/dda-service-0.1.jar
  except:
    - tags
  tags:
    - friday

release:
  image: registry.gitlab.com/gitlab-org/cloud-deploy/aws-base:latest
  stage: release
  script:
    - apt update
    - apt install -y wget
    - wget -O dd-java-agent.jar 'https://dtdg.co/latest-java-tracer'
    - docker login -u $CI_DEPENDENCY_PROXY_USER -p $CI_DEPENDENCY_PROXY_PASSWORD $CI_DEPENDENCY_PROXY_SERVER
    - docker build --build-arg DOCKER_IMAGE_MIRROR="${CI_DEPENDENCY_PROXY_GROUP_IMAGE_PREFIX}/" --build-arg APP_VERSION=$CI_COMMIT_SHORT_SHA -t $CONTAINER_RELEASE_IMAGE .
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    - docker push $CONTAINER_RELEASE_IMAGE
  rules:
    - if: $CI_PIPELINE_SOURCE == "schedule"
      when: never
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH || $CI_COMMIT_MESSAGE =~ $RELEASE_TRIGGER_COMMIT_MESSAGE
      when: on_success
  tags: [ friday ]

.release_environment: &release_environment
  - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
  - docker pull $CONTAINER_RELEASE_IMAGE
  - docker tag $CONTAINER_RELEASE_IMAGE $AWS_ECR_REPOSITORY:$CI_COMMIT_SHORT_SHA
  - aws configure set region $AWS_REGION
  - aws configure set aws_access_key_id $AWS_ACCESS_KEY_ID
  - aws configure set aws_secret_access_key $AWS_SECRET_ACCESS_KEY
  - aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $AWS_ECR_REPOSITORY
  - docker push $AWS_ECR_REPOSITORY:$CI_COMMIT_SHORT_SHA


.release-dda-files-lambda:
  image: registry.gitlab.com/gitlab-org/cloud-deploy/aws-base
  stage: release_environments
  when: manual
  allow_failure: false
  only:
    - main
  cache: {}
  script:
    - echo "S3 BUCKET => $S3_LAMBDA_BUCKET"
    - echo "VERSION => $VERSION"
    - echo "Criando zip com lambda e enviando ao S3..."
    - zip lambda_content.zip lambda_function.py
    - aws s3 cp lambda_content.zip s3://$S3_LAMBDA_BUCKET/dda-files_lambda/$VERSION/lambda_content.zip

.deploy-dda-files-lambda:
  image: "public.ecr.aws/docker/library/python:3.9"
  stage: deploy
  cache: { }
  tags: [ friday ]
  allow_failure: false
  only:
    - main
  before_script:
    - echo "AWS CONFIGURE"
    - python -V
    - pip install awscli --upgrade --user
    - export PATH=~/.local/bin:$PATH
    - aws configure set region $AWS_REGION
  script:
    - pip install boto3
    - 'python ./ci/publish_lambdas.py --output ./arns_by_function_name.json --s3-bucket $S3_LAMBDA_BUCKET --lambdas "dda-files dda-files_lambda/$VERSION/lambda_content.zip"'

release_friday_staging:
  image: registry.gitlab.com/gitlab-org/cloud-deploy/aws-base
  stage: release_environments
  environment:
    name: staging
  script:
    - *release_environment
  rules:
    - if: $CI_PIPELINE_SOURCE == "schedule"
      when: never
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH || $CI_COMMIT_MESSAGE =~ /#deploystg/
      when: on_success
  tags: [ friday ]

release_friday_production:
  image: registry.gitlab.com/gitlab-org/cloud-deploy/aws-base
  stage: release_environments
  when: manual
  allow_failure: true
  environment:
    name: production
  script:
    - *release_environment
  only:
    - main
  tags: [ friday ]

release_mepoupe_dda_files_lambda:
  environment:
    name: me-poupe
  extends: .release-dda-files-lambda
  variables:
    S3_LAMBDA_BUCKET: "677122980969-dda-files-lambda"

release_friday_dda_files_lambda:
  environment:
    name: production
  extends: .release-dda-files-lambda
  variables:
    S3_LAMBDA_BUCKET: "381563809177-dda-files-lambda"

release_me_poupe_staging:
  image: registry.gitlab.com/gitlab-org/cloud-deploy/aws-base
  stage: release_environments
  environment:
    name: me-poupe-stg
  script:
    - *release_environment
  rules:
    - if: $CI_PIPELINE_SOURCE == "schedule"
      when: never
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH || $CI_COMMIT_MESSAGE =~ /#deploymepoupestg/
      when: on_success
  tags: [ friday ]

release_me_poupe_production:
  image: registry.gitlab.com/gitlab-org/cloud-deploy/aws-base
  stage: release_environments
  when: manual
  allow_failure: true
  environment:
    name: me-poupe
  script:
    - *release_environment
  only:
    - main
  tags: [ friday ]

.deploy-ecs: &deploy-ecs
  - aws configure set region $AWS_REGION
  - TASK_DEFINITION=$(aws ecs describe-task-definition --task-definition "$TASK_DEFINITION_NAME")
  - NEW_TASK_DEFINITION=$(echo $TASK_DEFINITION | python $CI_PROJECT_DIR/ci/update_task_definition_image.py $AWS_ECR_REPOSITORY:$CI_COMMIT_SHORT_SHA)
  - aws ecs register-task-definition --family "${TASK_DEFINITION_NAME}" --container-definitions "${NEW_TASK_DEFINITION}" --requires-compatibilities "FARGATE" --network-mode awsvpc --task-role-arn arn:aws:iam::${AWS_ACCOUNT_ID}:role/${TASK_ROLE_NAME} --execution-role-arn arn:aws:iam::${AWS_ACCOUNT_ID}:role/${EXECUTION_ROLE_NAME} --cpu $CPU --memory $MEMORY
  - aws ecs update-service --cluster "${CLUSTER_NAME}" --service "${SERVICE_NAME}"  --task-definition "${TASK_DEFINITION_NAME}" --desired-count $DESIRED_COUNT

deploy_friday_staging:
  stage: deploy
  image:
    name: ${CI_DEPENDENCY_PROXY_GROUP_IMAGE_PREFIX}/amazon/aws-cli
    entrypoint: [ "" ]
  environment:
    name: staging
  script:
    - export MEMORY=1024
    - export CPU=256
    - export DESIRED_COUNT=1
    - *deploy-ecs
  variables:
    TASK_ROLE_NAME: dda-bills-task-role
    EXECUTION_ROLE_NAME: dda-bills-execution-role
  rules:
    - if: $CI_PIPELINE_SOURCE == "schedule"
      when: never
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH || $CI_COMMIT_MESSAGE =~ /#deploystg/
      when: on_success
  tags: [ friday ]

deploy-mepoupe-dda-files-lambda:
  needs: [
    release_mepoupe_dda_files_lambda
  ]
  environment:
    name: me-poupe
  extends:
    - .deploy-dda-files-lambda
  variables:
    S3_LAMBDA_BUCKET: "677122980969-dda-files-lambda"

deploy-friday-dda-files-lambda:
  needs: [
    release_friday_dda_files_lambda
  ]
  environment:
    name: production
  extends:
    - .deploy-dda-files-lambda
  variables:
    S3_LAMBDA_BUCKET: "381563809177-dda-files-lambda"

deploy_friday_production:
  stage: deploy
  needs: [ release_friday_production ]
  image:
    name: ${CI_DEPENDENCY_PROXY_GROUP_IMAGE_PREFIX}/amazon/aws-cli
    entrypoint: [ "" ]
  environment:
    name: production
  allow_failure: false
  script:
    - export MEMORY=8192
    - export CPU=4096
    - export DESIRED_COUNT=1
    - *deploy-ecs
  variables:
    TASK_ROLE_NAME: bill-payment-task-role
    EXECUTION_ROLE_NAME: myEcsTaskExecutionRole
  only:
    - main
  tags: [ friday ]

deploy_me_poupe_staging:
  stage: deploy
  image:
    name: ${CI_DEPENDENCY_PROXY_GROUP_IMAGE_PREFIX}/amazon/aws-cli
    entrypoint: [ "" ]
  environment:
    name: me-poupe-stg
  script:
    - export MEMORY=1024
    - export CPU=256
    - export DESIRED_COUNT=1
    - *deploy-ecs
  variables:
    TASK_ROLE_NAME: "me-poupe-dda-bills-task-role"
    EXECUTION_ROLE_NAME: "me-poupe-dda-bills-execution-role"
    TASK_DEFINITION_NAME: "me-poupe-dda-bills-task"
    CLUSTER_NAME: "me-poupe-bill-payment-cluster"
    SERVICE_NAME: "me-poupe-dda-bills-service"
  rules:
    - if: $CI_PIPELINE_SOURCE == "schedule"
      when: never
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH || $CI_COMMIT_MESSAGE =~ /#deploymepoupestg/
      when: on_success

deploy_me_poupe_production:
  stage: deploy
  needs: [ release_me_poupe_production ]
  image:
    name: ${CI_DEPENDENCY_PROXY_GROUP_IMAGE_PREFIX}/amazon/aws-cli
    entrypoint: [ "" ]
  environment:
    name: me-poupe
  allow_failure: false
  script:
    - export MEMORY=8192
    - export CPU=4096
    - export DESIRED_COUNT=1
    - *deploy-ecs
  variables:
    TASK_ROLE_NAME: "me-poupe-dda-bills-task-role"
    EXECUTION_ROLE_NAME: "me-poupe-dda-bills-execution-role"
    TASK_DEFINITION_NAME: "me-poupe-dda-bills-task"
    CLUSTER_NAME: "me-poupe-bill-payment-cluster"
    SERVICE_NAME: "me-poupe-dda-bills-service"
  only:
    - main
  tags: [ friday ]