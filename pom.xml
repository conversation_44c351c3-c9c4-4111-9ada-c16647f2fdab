<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>ai.friday</groupId>
    <artifactId>dda-service</artifactId>
    <version>0.1</version>
    <packaging>${packaging}</packaging>

    <parent>
        <groupId>io.micronaut.platform</groupId>
        <artifactId>micronaut-parent</artifactId>
        <version>4.8.2</version>
    </parent>

    <properties>
        <packaging>jar</packaging>
        <jdk.version>17</jdk.version>
        <release.version>17</release.version>
        <micronaut.runtime>netty</micronaut.runtime>
        <reator-kotlin-extension.version>1.2.2</reator-kotlin-extension.version>
        <exec.mainClass>ai.friday.dda.ApplicationKt</exec.mainClass>
        <shedlock.version>4.44.0</shedlock.version>
        <maven-surefire-plugin.version>3.2.5</maven-surefire-plugin.version>
        <arrow.version>1.1.2</arrow.version>
        <micronaut-tracing.version>7.0.0</micronaut-tracing.version>
        <opentelemetry.alpha.version>1.48.0-alpha</opentelemetry.alpha.version>
    </properties>

    <repositories>
        <repository>
            <id>central</id>
            <url>https://repo.maven.apache.org/maven2</url>
        </repository>
        <repository>
            <id>dynamodb-local-oregon</id>
            <name>DynamoDB Local Release Repository</name>
            <url>https://s3-us-west-2.amazonaws.com/dynamodb-local/release</url>
        </repository>
    </repositories>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>net.javacrumbs.shedlock</groupId>
                <artifactId>shedlock-bom</artifactId>
                <version>${shedlock.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>io.micronaut.tracing</groupId>
                <artifactId>micronaut-tracing-bom</artifactId>
                <version>${micronaut-tracing.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <!-- Core -->
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-reflect</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-stdlib-jdk8</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlinx</groupId>
            <artifactId>kotlinx-coroutines-core-jvm</artifactId>
        </dependency>

        <!-- Micronaut -->
        <dependency>
            <groupId>io.micronaut.kotlin</groupId>
            <artifactId>micronaut-kotlin-runtime</artifactId>
        </dependency>
        <dependency>
            <groupId>io.micronaut.reactor</groupId>
            <artifactId>micronaut-reactor</artifactId>
        </dependency>
        <dependency>
            <groupId>io.micronaut</groupId>
            <artifactId>micronaut-management</artifactId>
        </dependency>
        <dependency>
            <groupId>io.micronaut</groupId>
            <artifactId>micronaut-inject</artifactId>
        </dependency>
        <dependency>
            <groupId>io.micronaut</groupId>
            <artifactId>micronaut-http-client</artifactId>
        </dependency>
        <dependency>
            <groupId>io.micronaut.reactor</groupId>
            <artifactId>micronaut-reactor-http-client</artifactId>
        </dependency>
        <dependency>
            <groupId>io.micronaut</groupId>
            <artifactId>micronaut-http-server-netty</artifactId>
        </dependency>
        <dependency>
            <groupId>io.micronaut.kafka</groupId>
            <artifactId>micronaut-kafka</artifactId>
        </dependency>
        <dependency>
            <groupId>io.micronaut</groupId>
            <artifactId>micronaut-jackson-databind</artifactId>
        </dependency>
        <dependency>
            <groupId>io.micronaut.validation</groupId>
            <artifactId>micronaut-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>io.micronaut.aws</groupId>
            <artifactId>micronaut-aws-sdk-v2</artifactId>
        </dependency>
        <dependency>
            <groupId>io.micronaut.cache</groupId>
            <artifactId>micronaut-cache-core</artifactId>
        </dependency>
        <dependency>
            <groupId>io.micronaut.cache</groupId>
            <artifactId>micronaut-cache-caffeine</artifactId>
        </dependency>
        <dependency>
            <groupId>io.micronaut.security</groupId>
            <artifactId>micronaut-security-jwt</artifactId>
        </dependency>
        <dependency>
            <groupId>io.micronaut.micrometer</groupId>
            <artifactId>micronaut-micrometer-core</artifactId>
        </dependency>
        <dependency>
            <groupId>io.micronaut.micrometer</groupId>
            <artifactId>micronaut-micrometer-registry-statsd</artifactId>
        </dependency>
        <dependency>
            <groupId>io.micronaut.redis</groupId>
            <artifactId>micronaut-redis-lettuce</artifactId>
            <scope>compile</scope>
        </dependency>

        <!-- AWS -->
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>s3</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>software.amazon.awssdk</groupId>
                    <artifactId>apache-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>software.amazon.awssdk</groupId>
                    <artifactId>netty-nio-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>sqs</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>software.amazon.awssdk</groupId>
                    <artifactId>apache-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>software.amazon.awssdk</groupId>
                    <artifactId>netty-nio-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>dynamodb</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>software.amazon.awssdk</groupId>
                    <artifactId>apache-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>software.amazon.awssdk</groupId>
                    <artifactId>netty-nio-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>dynamodb-enhanced</artifactId>
        </dependency>
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>url-connection-client</artifactId>
        </dependency>

        <!-- Others -->
        <dependency>
            <groupId>io.zipkin.brave</groupId>
            <artifactId>brave-instrumentation-http</artifactId>
            <version>5.13.7</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>io.opentracing.brave</groupId>
            <artifactId>brave-opentracing</artifactId>
            <version>1.0.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>io.projectreactor.kotlin</groupId>
            <artifactId>reactor-kotlin-extensions</artifactId>
            <version>${reator-kotlin-extension.version}</version> <!--  -->
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.module</groupId>
            <artifactId>jackson-module-kotlin</artifactId>
        </dependency>
        <dependency>
            <groupId>jakarta.annotation</groupId>
            <artifactId>jakarta.annotation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
            <version>8.0</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.17.0</version>
        </dependency>
        <dependency>
            <groupId>io.arrow-kt</groupId>
            <artifactId>arrow-core</artifactId>
            <version>${arrow.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jcraft</groupId>
            <artifactId>jsch</artifactId>
            <version>0.1.55</version>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcpg-jdk18on</artifactId>
            <version>1.79</version>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk18on</artifactId>
            <version>1.79</version>
        </dependency>
        <dependency>
            <groupId>net.javacrumbs.shedlock</groupId>
            <artifactId>shedlock-provider-dynamodb2</artifactId>
        </dependency>

        <dependency>
            <groupId>io.micronaut.tracing</groupId>
            <artifactId>micronaut-tracing-core</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>io.micronaut.tracing</groupId>
            <artifactId>micronaut-tracing-opentelemetry</artifactId>
        </dependency>
        <dependency>
            <groupId>io.micronaut.tracing</groupId>
            <artifactId>micronaut-tracing-opentelemetry-kafka</artifactId>
        </dependency>
        <dependency>
            <groupId>io.opentelemetry</groupId>
            <artifactId>opentelemetry-extension-kotlin</artifactId>
        </dependency>
        <dependency>
            <groupId>io.opentelemetry.instrumentation</groupId>
            <artifactId>opentelemetry-logback-mdc-1.0</artifactId>
        </dependency>

        <!-- Tests -->
        <dependency>
            <groupId>io.mockk</groupId>
            <artifactId>mockk</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.mockk</groupId>
            <artifactId>mockk-jvm</artifactId>
            <version>1.13.14</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.micronaut.test</groupId>
            <artifactId>micronaut-test-kotest5</artifactId>
            <version>4.7.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.kotest.extensions</groupId>
            <artifactId>kotest-assertions-arrow-jvm</artifactId>
            <version>1.4.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.kotest</groupId>
            <artifactId>kotest-runner-junit5-jvm</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.kotest</groupId>
            <artifactId>kotest-assertions-core-jvm</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.tngtech.archunit</groupId>
            <artifactId>archunit-junit5</artifactId>
            <version>1.3.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>DynamoDBLocal</artifactId>
            <version>2.0.0</version>
            <scope>test</scope>
        </dependency>
        <!-- Add servlet API for DynamoDB Local compatibility with Java 17 -->
        <dependency>
            <groupId>jakarta.servlet</groupId>
            <artifactId>jakarta.servlet-api</artifactId>
            <version>6.0.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcpkix-jdk18on</artifactId>
            <version>1.79</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.jacoco</groupId>
                    <artifactId>jacoco-maven-plugin</artifactId>
                    <version>0.8.12</version>
                    <executions>
                        <execution>
                            <id>default-prepare-agent</id>
                            <goals>
                                <goal>prepare-agent</goal>
                            </goals>
                        </execution>
                        <execution>
                            <id>default-report</id>
                            <goals>
                                <goal>report</goal>
                            </goals>
                        </execution>
                        <execution>
                            <id>default-check</id>
                            <goals>
                                <goal>check</goal>
                            </goals>
                            <configuration>
                                <rules>
                                    <rule>
                                        <element>BUNDLE</element>
                                        <limits>
                                            <limit>
                                                <counter>COMPLEXITY</counter>
                                                <value>COVEREDRATIO</value>
                                                <minimum>0.60</minimum>
                                            </limit>
                                        </limits>
                                    </rule>
                                </rules>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <plugin>
                <groupId>io.micronaut.maven</groupId>
                <artifactId>micronaut-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <includes>
                        <include>**/*Spec.*</include>
                        <include>**/*Test.*</include>
                    </includes>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>org.junit.jupiter</groupId>
                        <artifactId>junit-jupiter-engine</artifactId>
                        <version>${junit5.version}</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <artifactId>kotlin-maven-plugin</artifactId>
                <groupId>org.jetbrains.kotlin</groupId>
                <version>${kotlin.version}</version>
                <configuration>
                    <jvmTarget>${jdk.version}</jvmTarget>
                    <compilerPlugins>
                        <plugin>all-open</plugin>
                    </compilerPlugins>
                    <pluginOptions>
                        <option>all-open:annotation=io.micronaut.aop.Around</option>
                    </pluginOptions>
                </configuration>
                <executions>
                    <execution>
                        <id>kapt</id>
                        <goals>
                            <goal>kapt</goal>
                        </goals>
                        <configuration>
                            <sourceDirs>
                                <sourceDir>${project.basedir}/src/main/kotlin</sourceDir>
                            </sourceDirs>
                            <annotationProcessorPaths combine.self="override">
                                <annotationProcessorPath>
                                    <groupId>io.micronaut</groupId>
                                    <artifactId>micronaut-inject-java</artifactId>
                                    <version>${micronaut.version}</version>
                                </annotationProcessorPath>
                                <annotationProcessorPath>
                                    <groupId>io.micronaut</groupId>
                                    <artifactId>micronaut-graal</artifactId>
                                    <version>${micronaut.version}</version>
                                </annotationProcessorPath>
                                <annotationProcessorPath>
                                    <groupId>io.micronaut</groupId>
                                    <artifactId>micronaut-graal</artifactId>
                                    <version>${micronaut.version}</version>
                                </annotationProcessorPath>
                                <annotationProcessorPath>
                                    <groupId>io.micronaut</groupId>
                                    <artifactId>micronaut-http-validation</artifactId>
                                    <version>${micronaut.version}</version>
                                </annotationProcessorPath>
                                <annotationProcessorPath>
                                    <groupId>io.micronaut.validation</groupId>
                                    <artifactId>micronaut-validation-processor</artifactId>
                                    <version>${micronaut.validation.version}</version>
                                </annotationProcessorPath>
                                <annotationProcessorPath>
                                    <groupId>io.micronaut.security</groupId>
                                    <artifactId>micronaut-security-annotations</artifactId>
                                    <version>4.12.0</version>
                                </annotationProcessorPath>
                                <annotationProcessorPath>
                                    <groupId>io.micronaut.tracing</groupId>
                                    <artifactId>micronaut-tracing-opentelemetry-annotation</artifactId>
                                    <version>${micronaut-tracing.version}</version>
                                </annotationProcessorPath>
                            </annotationProcessorPaths>
                            <annotationProcessorArgs>
                                <annotationProcessorArg>micronaut.processing.group=ai.friday</annotationProcessorArg>
                                <annotationProcessorArg>micronaut.processing.module=ddaService</annotationProcessorArg>
                            </annotationProcessorArgs>
                        </configuration>
                    </execution>
                    <execution>
                        <id>compile</id>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                        <configuration>
                            <sourceDirs>
                                <sourceDir>${project.basedir}/src/main/kotlin</sourceDir>
                            </sourceDirs>
                        </configuration>
                    </execution>
                    <execution>
                        <id>test-kapt</id>
                        <goals>
                            <goal>test-kapt</goal>
                        </goals>
                        <configuration>
                            <sourceDirs>
                                <sourceDir>${project.basedir}/src/test/kotlin</sourceDir>
                            </sourceDirs>
                            <annotationProcessorPaths combine.self="override">
                                <annotationProcessorPath>
                                    <groupId>io.micronaut</groupId>
                                    <artifactId>micronaut-inject-java</artifactId>
                                    <version>${micronaut.version}</version>
                                </annotationProcessorPath>
                                <annotationProcessorPath>
                                    <groupId>io.micronaut.validation</groupId>
                                    <artifactId>micronaut-validation-processor</artifactId>
                                    <version>${micronaut.validation.version}</version>
                                </annotationProcessorPath>
                                <annotationProcessorPath>
                                    <groupId>io.micronaut.security</groupId>
                                    <artifactId>micronaut-security-annotations</artifactId>
                                    <version>4.12.0</version>
                                </annotationProcessorPath>
                            </annotationProcessorPaths>
                        </configuration>
                    </execution>
                    <execution>
                        <id>test-compile</id>
                        <goals>
                            <goal>test-compile</goal>
                        </goals>
                        <configuration>
                            <sourceDirs>
                                <sourceDir>${project.basedir}/src/test/kotlin</sourceDir>
                                <sourceDir>${project.basedir}/target/generated-sources/kapt/test</sourceDir>
                            </sourceDirs>
                        </configuration>
                    </execution>
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>org.jetbrains.kotlin</groupId>
                        <artifactId>kotlin-maven-allopen</artifactId>
                        <version>${kotlin.version}</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
                <version>3.1.0</version>
                <executions>
                    <execution>
                        <id>ktlint</id>
                        <phase>verify</phase>
                        <configuration>
                            <target name="ktlint">
                                <java taskname="ktlint" dir="${basedir}" fork="true" failonerror="true"
                                      classname="com.pinterest.ktlint.Main" classpathref="maven.plugin.classpath">
                                    <arg value="src/**/*.kt"/>
                                </java>
                            </target>
                        </configuration>
                        <goals>
                            <goal>run</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>ktlint-format</id>
                        <configuration>
                            <target name="ktlint">
                                <java taskname="ktlint" dir="${basedir}" fork="true" failonerror="true"
                                      classname="com.pinterest.ktlint.Main" classpathref="maven.plugin.classpath">
                                    <arg value="-F"/>
                                    <arg value="src/**/*.kt"/>
                                </java>
                            </target>
                        </configuration>
                        <goals>
                            <goal>run</goal>
                        </goals>
                    </execution>
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>com.pinterest</groupId>
                        <artifactId>ktlint</artifactId>
                        <version>1.5.0</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <executions>
                    <execution>
                        <id>compile</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>testCompile</id>
                        <phase>test-compile</phase>
                        <goals>
                            <goal>testCompile</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
